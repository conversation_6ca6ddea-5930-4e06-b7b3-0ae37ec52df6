<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
          <el-option label="待支付" value="0" />
          <el-option label="支付成功" value="1" />
          <el-option label="支付失败" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="payType">
        <el-select v-model="queryParams.payType" placeholder="请选择支付方式" clearable>
          <el-option label="支付宝" value="1" />
          <el-option label="微信" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:payment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>待支付订单</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.pendingPayments }}</span>
            <span class="amount">¥{{ statistics.pendingAmount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付成功订单</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.successPayments }}</span>
            <span class="amount">¥{{ statistics.successAmount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付失败订单</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.failedPayments }}</span>
            <span class="amount">¥{{ statistics.failedAmount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总计</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.totalPayments }}</span>
            <span class="amount">¥{{ statistics.totalAmount }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="mallPaymentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="支付ID" align="center" prop="paymentId" />
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="支付金额" align="center" prop="payAmount" />
      <el-table-column label="支付方式" align="center" prop="payType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.pay_type" :value="scope.row.payType"/>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.pay_status" :value="scope.row.payStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="payTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.payTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['mall:payment:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleRetry(scope.row)"
            v-hasPermi="['mall:payment:edit']"
            v-if="scope.row.payStatus === '0' || scope.row.payStatus === '2'"
          >重新支付</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 支付详情对话框 -->
    <el-dialog title="支付详情" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="支付ID">
          <el-input v-model="form.paymentId" :disabled="true" />
        </el-form-item>
        <el-form-item label="订单ID">
          <el-input v-model="form.orderId" :disabled="true" />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="form.orderNo" :disabled="true" />
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="form.userId" :disabled="true" />
        </el-form-item>
        <el-form-item label="支付金额">
          <el-input v-model="form.payAmount" :disabled="true" />
        </el-form-item>
        <el-form-item label="支付方式">
          <dict-tag :options="dict.type.pay_type" :value="form.payType"/>
        </el-form-item>
        <el-form-item label="支付状态">
          <dict-tag :options="dict.type.pay_status" :value="form.payStatus"/>
        </el-form-item>
        <el-form-item label="第三方交易号">
          <el-input v-model="form.tradeNo" :disabled="true" />
        </el-form-item>
        <el-form-item label="支付时间">
          <el-input v-model="form.payTime" :disabled="true" />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-input v-model="form.createTime" :disabled="true" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMallPayment, getMallPayment, exportMallPayment, getMallPaymentStatistics, retryMallPayment } from "@/api/mall/payment";

export default {
  name: "MallPayment",
  dicts: ['pay_type', 'pay_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商城支付记录表格数据
      mallPaymentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        payStatus: null,
        payType: null,
      },
      // 表单参数
      form: {},
      // 统计数据
      statistics: {
        pendingPayments: 0,
        pendingAmount: 0,
        successPayments: 0,
        successAmount: 0,
        failedPayments: 0,
        failedAmount: 0,
        totalPayments: 0,
        totalAmount: 0
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询商城支付记录列表 */
    getList() {
      this.loading = true;
      listMallPayment(this.queryParams).then(response => {
        this.mallPaymentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getMallPaymentStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        paymentId: null,
        orderId: null,
        orderNo: null,
        userId: null,
        payAmount: null,
        payType: null,
        payStatus: null,
        tradeNo: null,
        payTime: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.paymentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.reset();
      const paymentId = row.paymentId || this.ids
      getMallPayment(paymentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "支付详情";
      });
    },
    /** 重新支付按钮操作 */
    handleRetry(row) {
      const paymentId = row.paymentId;
      this.$modal.confirm('是否确认重新发起支付ID为"' + paymentId + '"的支付记录?').then(function() {
        return retryMallPayment(paymentId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("重新发起支付成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mall/payment/export', {
        ...this.queryParams
      }, `mallPayment_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.count {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  display: block;
}
.amount {
  font-size: 16px;
  color: #67C23A;
  display: block;
  margin-top: 5px;
}
</style>
