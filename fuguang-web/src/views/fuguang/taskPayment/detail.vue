<template>
  <div class="app-container" v-loading="loading">
    <!-- 错误提示 -->
    <el-alert v-if="!paymentId" title="错误" type="error" description="缺少支付ID参数" show-icon></el-alert>

    <el-card class="box-card" v-if="paymentId">
      <div slot="header" class="clearfix">
        <span>任务支付详情 (ID: {{ paymentId }})</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <!-- 支付信息 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="never">
            <div slot="header">
              <span>支付信息</span>
            </div>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="支付ID">
                {{ form.paymentId }}
              </el-descriptions-item>
              <el-descriptions-item label="任务ID">
                {{ form.taskId }}
              </el-descriptions-item>
              <el-descriptions-item label="订单号">
                {{ form.orderNo }}
              </el-descriptions-item>
              <el-descriptions-item label="用户ID">
                {{ form.userId }}
              </el-descriptions-item>
              <el-descriptions-item label="支付金额">
                <el-tag type="danger" size="medium">¥{{ form.payAmount }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="支付方式">
                <dict-tag :options="dict.type.pay_type" :value="form.payType"/>
              </el-descriptions-item>
              <el-descriptions-item label="支付状态">
                <dict-tag :options="dict.type.pay_status" :value="form.payStatus"/>
              </el-descriptions-item>
              <el-descriptions-item label="第三方交易号">
                {{ form.tradeNo || '暂无' }}
              </el-descriptions-item>
              <el-descriptions-item label="支付时间">
                {{ form.payTime || '暂无' }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间" :span="2">
                {{ form.createTime }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ form.updateTime }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row :gutter="20" style="margin-top: 20px;" v-if="form.remark">
        <el-col :span="24">
          <el-card shadow="never">
            <div slot="header">
              <span>备注信息</span>
            </div>
            <div class="remark-content">
              {{ form.remark }}
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getTaskPayment } from "@/api/fuguang/taskPayment";

export default {
  name: "TaskPaymentDetail",
  dicts: ['pay_type', 'pay_status'],
  data() {
    return {
      paymentId: null,
      loading: false,
      form: {
        paymentId: null,
        taskId: null,
        orderNo: '',
        userId: null,
        payAmount: 0,
        payType: null,
        payStatus: null,
        tradeNo: '',
        payTime: '',
        createTime: '',
        updateTime: '',
        remark: ''
      }
    };
  },
  created() {
    this.paymentId = this.$route.params.paymentId;
    console.log('TaskPaymentDetail created, paymentId:', this.paymentId);
    if (this.paymentId) {
      this.getPaymentDetail();
    } else {
      this.$modal.msgError("缺少支付ID参数");
    }
  },
  methods: {
    /** 获取支付详情 */
    getPaymentDetail() {
      this.loading = true;
      getTaskPayment(this.paymentId).then(response => {
        if (response.data) {
          this.form = { ...this.form, ...response.data };
        }
        this.loading = false;
      }).catch(error => {
        console.error('获取支付详情失败:', error);
        this.$modal.msgError("获取支付详情失败");
        this.loading = false;
      });
    },
    /** 返回上一页 */
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.remark-content {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 60px;
  line-height: 1.6;
}
</style>
