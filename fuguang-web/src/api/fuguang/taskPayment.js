import request from '@/utils/request'

// 查询任务支付记录列表
export function listTaskPayment(query) {
  return request({
    url: '/fuguang/taskPayment/list',
    method: 'get',
    params: query
  })
}

// 查询任务支付记录详细
export function getTaskPayment(paymentId) {
  return request({
    url: '/fuguang/taskPayment/' + paymentId,
    method: 'get'
  })
}

// 新增任务支付记录
export function addTaskPayment(data) {
  return request({
    url: '/fuguang/taskPayment',
    method: 'post',
    data: data
  })
}

// 修改任务支付记录
export function updateTaskPayment(data) {
  return request({
    url: '/fuguang/taskPayment',
    method: 'put',
    data: data
  })
}

// 删除任务支付记录
export function delTaskPayment(paymentId) {
  return request({
    url: '/fuguang/taskPayment/' + paymentId,
    method: 'delete'
  })
}

// 导出任务支付记录
export function exportTaskPayment(query) {
  return request({
    url: '/fuguang/taskPayment/export',
    method: 'post',
    params: query
  })
}

// 根据订单号查询支付记录
export function getTaskPaymentByOrderNo(orderNo) {
  return request({
    url: '/fuguang/taskPayment/orderNo/' + orderNo,
    method: 'get'
  })
}

// 查询支付状态
export function getTaskPaymentStatus(orderNo) {
  return request({
    url: '/fuguang/taskPayment/status/' + orderNo,
    method: 'get'
  })
}

// 获取任务支付统计信息
export function getTaskPaymentStatistics() {
  return request({
    url: '/fuguang/taskPayment/statistics',
    method: 'get'
  })
}
