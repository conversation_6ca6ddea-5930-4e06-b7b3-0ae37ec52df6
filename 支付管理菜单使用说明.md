# 支付管理菜单使用说明

## 概述

本文档介绍了fuguang-api项目中支付管理模块的菜单配置和使用方法。支付管理模块提供了完整的支付功能，包括任务支付、商城支付、线下支付的管理，以及支付配置、统计分析和回调日志等功能。

## 菜单结构

```
支付管理
├── 任务支付管理
├── 商城支付管理  
├── 线下支付管理
├── 支付配置管理
├── 支付统计分析
└── 支付回调日志
```

## 安装步骤

### 1. 执行SQL脚本

首先执行 `fuguang-api/sql/payment_menu.sql` 文件中的SQL语句，将菜单权限配置添加到数据库中：

```sql
-- 在数据库中执行以下文件
source fuguang-api/sql/payment_menu.sql;
```

### 2. 重启后端服务

重启 `fuguang-api` 后端服务，使菜单配置生效。

### 3. 分配菜单权限

登录管理后台，进入 **系统管理 > 角色管理**，为相应的角色分配支付管理相关的菜单权限。

## 功能模块详细说明

### 1. 任务支付管理 (`/fuguang/taskPayment`)

**功能描述：** 管理任务相关的支付记录

**主要功能：**
- 查看任务支付记录列表
- 支付记录详情查看
- 支付状态统计（待支付、成功、失败）
- 支付记录导出
- 按订单号、支付状态、支付方式筛选

**权限标识：**
- `fuguang:taskPayment:list` - 查询列表
- `fuguang:taskPayment:query` - 查看详情
- `fuguang:taskPayment:export` - 导出数据

### 2. 商城支付管理 (`/mall/payment`)

**功能描述：** 管理商城订单的支付记录

**主要功能：**
- 查看商城支付记录列表
- 支付记录详情查看
- 重新发起支付功能
- 支付状态统计
- 支付记录导出

**权限标识：**
- `mall:payment:list` - 查询列表
- `mall:payment:query` - 查看详情
- `mall:payment:edit` - 重新发起支付
- `mall:payment:export` - 导出数据

### 3. 线下支付管理 (`/fuguang/offlinePayment`)

**功能描述：** 管理线下支付和转账记录

**主要功能：**
- 查看线下支付记录
- 转账状态管理
- 转账操作处理

**权限标识：**
- `fuguang:offlinePayment:list` - 查询列表
- `fuguang:offlinePayment:query` - 查看详情
- `fuguang:offlinePayment:edit` - 编辑转账状态

### 4. 支付配置管理 (`/payment/config`)

**功能描述：** 配置支付宝、微信支付等支付渠道参数

**主要功能：**
- 支付宝配置（公钥模式/证书模式）
- 微信支付配置
- 系统支付参数配置
- 支付连接测试
- 证书文件上传

**权限标识：**
- `payment:config:query` - 查看配置
- `payment:config:edit` - 修改配置
- `payment:config:test` - 测试连接

### 5. 支付统计分析 (`/payment/statistics`)

**功能描述：** 提供支付数据的统计分析和可视化展示

**主要功能：**
- 支付总览统计（今日支付金额、笔数、成功率）
- 支付趋势分析图表
- 支付方式分布饼图
- 各业务类型支付统计
- 支付异常监控

**权限标识：**
- `payment:statistics:query` - 查看统计
- `payment:statistics:trend` - 趋势分析
- `payment:statistics:channel` - 渠道分析
- `payment:statistics:exception` - 异常分析

### 6. 支付回调日志 (`/payment/callback`)

**功能描述：** 管理支付宝、微信等第三方支付的回调日志

**主要功能：**
- 查看回调日志列表
- 回调详情查看（包含回调参数）
- 失败回调重试
- 回调统计信息
- 回调日志导出

**权限标识：**
- `payment:callback:list` - 查询列表
- `payment:callback:query` - 查看详情
- `payment:callback:retry` - 重试回调
- `payment:callback:export` - 导出日志

## 字典配置

系统已自动添加以下字典类型：

### 支付类型 (`pay_type`)
- `1` - 支付宝
- `2` - 微信支付  
- `3` - 银联支付

### 支付状态 (`pay_status`)
- `0` - 待支付
- `1` - 支付成功
- `2` - 支付失败
- `3` - 已退款

### 转账状态 (`transfer_status`)
- `0` - 未转账
- `1` - 转账中
- `2` - 转账成功
- `3` - 转账失败

## 路由配置

前端路由已自动配置，主要路由如下：

```javascript
// 任务支付管理
/fuguang/taskPayment

// 商城支付管理  
/mall/payment

// 线下支付管理
/fuguang/offlinePayment

// 支付配置管理
/payment/config

// 支付统计分析
/payment/statistics

// 支付回调日志
/payment/callback
```

## 注意事项

1. **权限分配：** 请根据实际需要为不同角色分配相应的菜单权限
2. **证书配置：** 使用支付宝证书模式时，需要上传相应的证书文件
3. **回调地址：** 确保支付宝回调地址配置为：`/payment/alipay/notify`
4. **数据安全：** 支付相关的敏感信息请妥善保管，避免泄露

## 技术支持

如有问题，请联系开发团队或查看相关技术文档。
