package com.ruoyi.app.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * APP商家线下支付管理控制器
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Api(tags = "APP商家线下支付", description = "APP商家线下支付相关接口")
@RestController("MerchantOfflinePaymentController")
@RequestMapping("/app/merchant/offlinePayment")
public class MerchantOfflinePaymentController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(MerchantOfflinePaymentController.class);

    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    /**
     * 生成商家二维码
     */
    @ApiOperation("生成商家二维码")
    @GetMapping("/{merchantId}/qrcode")
    public AjaxResult generateMerchantQrcode(@ApiParam("商家ID") @PathVariable Long merchantId)
    {
        try {
            Map<String, Object> result = offlinePaymentService.generateMerchantQrcode(merchantId);

            if ((Boolean) result.get("success")) {
                return success(result.get("qrcode"));
            } else {
                return error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("生成商家二维码异常，商家ID：{}", merchantId, e);
            return error("生成商家二维码失败：" + e.getMessage());
        }
    }

    /**
     * 获取商家二维码
     */
    @ApiOperation("获取商家二维码")
    @GetMapping("/{merchantId}/qrcode/info")
    public AjaxResult getMerchantQrcode(@ApiParam("商家ID") @PathVariable Long merchantId)
    {
        try {
            return success(offlinePaymentService.getMerchantQrcode(merchantId));
        } catch (Exception e) {
            log.error("获取商家二维码异常，商家ID：{}", merchantId, e);
            return error("获取商家二维码失败：" + e.getMessage());
        }
    }

    /**
     * 创建线下支付订单
     */
    @ApiOperation("创建线下支付订单")
    @PostMapping("/create")
    public AjaxResult createOfflinePayOrder(@RequestParam @ApiParam("商家ID") Long merchantId,
                                            @RequestParam @ApiParam("支付金额") BigDecimal payAmount)
    {
        try {
            if (merchantId == null || payAmount == null || payAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return error("参数错误");
            }
            Map<String, Object> result = offlinePaymentService.createOfflinePayOrder(merchantId, payAmount);
            if ((Boolean) result.get("success")) {
                return success(result);
            } else {
                return error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("创建线下支付订单异常，商家ID：{}，金额：{}", merchantId, payAmount, e);
            return error("创建线下支付订单失败：" + e.getMessage());
        }
    }
}
