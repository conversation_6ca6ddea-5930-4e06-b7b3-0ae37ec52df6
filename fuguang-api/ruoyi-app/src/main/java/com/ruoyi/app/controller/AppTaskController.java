package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP任务接口控制器
 * 提供任务查询、发布、接取、完成等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP任务接口", description = "APP任务管理相关接口")
@RestController("appTaskApiController")
@RequestMapping("/app/task")
public class AppTaskController extends BaseController
{
    @Autowired
    private IAppTaskService appTaskService;

    /**
     * 查询任务列表
     * 支持按条件筛选任务，包括任务类型、状态、地理位置等
     *
     * @param appTask 查询条件
     * @return 任务列表
     */
    @Anonymous
    @ApiOperation(value = "查询任务列表",
                  notes = "获取任务列表，支持分页和条件筛选，包括任务类型、状态、地理位置等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回任务列表")
    })
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(value = "查询条件", required = false) AppTask appTask)
    {
        startPage();
        List<AppTask> list = appTaskService.selectAppTaskList(appTask);
        return getDataTable(list);
    }

    /**
     * 获取任务详细信息
     * 查看任务详情时会自动增加浏览次数
     *
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    @ApiOperation(value = "获取任务详细信息",
                  notes = "根据任务ID获取任务详细信息，会自动增加浏览次数")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回任务详细信息"),
        @ApiResponse(code = 500, message = "任务不存在")
    })
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@ApiParam(value = "任务ID", required = true) @PathVariable("taskId") Long taskId)
    {
        // 增加浏览次数
        appTaskService.increaseViewCount(taskId);
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        return success(task);
    }

    /**
     * 发布新任务
     * 用户发布新的任务，需要填写任务详情和报酬
     *
     * @param appTask 任务信息
     * @return 发布结果
     */
    @ApiOperation(value = "发布新任务",
                  notes = "用户发布新任务，需要填写任务标题、描述、报酬、截止时间等信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "发布成功"),
        @ApiResponse(code = 500, message = "发布失败，参数错误或余额不足")
    })
    @Log(title = "APP任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "任务信息", required = true) @RequestBody AppTask appTask)
    {
        Long userId = getUserId();
        appTask.setPublisherId(userId);
        try {
            // 发布任务
            return success( appTaskService.insertAppTask(appTask));
        } catch (Exception e) {
            return error("任务发布失败：" + e.getMessage());
        }
    }

    /**
     * 修改任务
     */
    @ApiOperation("修改任务")
    @Log(title = "APP任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppTask appTask)
    {
        Long userId = getUserId();
        // 验证是否为任务发布者
        AppTask existTask = appTaskService.selectAppTaskByTaskId(appTask.getTaskId());
        if (existTask == null || !existTask.getPublisherId().equals(userId)) {
            return error("无权限修改此任务");
        }
        
        return toAjax(appTaskService.updateAppTask(appTask));
    }

    /**
     * 接取任务
     * 用户接取发布的任务，接取后任务状态变为进行中
     *
     * @param taskId 任务ID
     * @return 接取结果
     */
    @ApiOperation(value = "接取任务",
                  notes = "用户接取发布的任务，接取成功后任务状态变为进行中，不能接取自己发布的任务")
    @ApiResponses({
        @ApiResponse(code = 200, message = "接取成功"),
        @ApiResponse(code = 500, message = "接取失败，任务不存在、状态不允许或不能接取自己的任务")
    })
    @Log(title = "接取任务", businessType = BusinessType.UPDATE)
    @PostMapping("/accept/{taskId}")
    public AjaxResult acceptTask(@ApiParam(value = "任务ID", required = true) @PathVariable Long taskId)
    {
        Long userId = getUserId();

        // 验证任务状态
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if (!"0".equals(task.getTaskStatus())) {
            return error("任务状态不允许接取");
        }
        if (task.getPublisherId().equals(userId)) {
            return error("不能接取自己发布的任务");
        }

        int result = appTaskService.acceptTask(taskId, userId);
        return toAjax(result);
    }

    /**
     * 完成任务
     * 任务接收者提交任务完成，等待发布者确认
     *
     * @param taskId 任务ID
     * @return 完成结果
     */
    @ApiOperation(value = "完成任务",
                  notes = "任务接收者提交任务完成，任务状态变为待确认，等待发布者确认")
    @ApiResponses({
        @ApiResponse(code = 200, message = "提交成功，等待发布者确认"),
        @ApiResponse(code = 500, message = "提交失败，任务不存在、状态不允许或无权限操作")
    })
    @Log(title = "完成任务", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{taskId}")
    public AjaxResult completeTask(@ApiParam(value = "任务ID", required = true) @PathVariable Long taskId)
    {
        Long userId = getUserId();
        // 验证任务状态和权限
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if (!"1".equals(task.getTaskStatus())) {
            return error("任务状态不允许完成");
        }
        if (!task.getReceiverId().equals(userId)) {
            return error("只有任务接收者可以完成任务");
        }
        return appTaskService.completeTask(taskId);
    }

    /**
     * 取消任务
     */
    @ApiOperation("取消任务")
    @Log(title = "取消任务", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{taskId}")
    public AjaxResult cancelTask(@PathVariable Long taskId)
    {
        Long userId = getUserId();
        
        // 验证任务状态和权限
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if ("2".equals(task.getTaskStatus()) || "3".equals(task.getTaskStatus())) {
            return error("任务已完成或已取消");
        }
        if (!task.getPublisherId().equals(userId)) {
            return error("只有任务发布者可以取消任务");
        }
        
        int result = appTaskService.cancelTask(taskId);
        return toAjax(result);
    }

    /**
     * 查询我发布的任务
     */
    @ApiOperation("查询我发布的任务")
    @GetMapping("/my-published")
    public AjaxResult getMyPublishedTasks()
    {
        Long userId = getUserId();
        List<AppTask> tasks = appTaskService.selectTasksByPublisher(userId);
        return success(tasks);
    }

    /**
     * 查询我接取的任务
     */
    @ApiOperation("查询我接取的任务")
    @GetMapping("/my-received")
    public AjaxResult getMyReceivedTasks()
    {
        Long userId = getUserId();
        List<AppTask> tasks = appTaskService.selectTasksByReceiver(userId);
        return success(tasks);
    }

    /**
     * 查询热门任务
     */
    @Anonymous
    @ApiOperation("查询热门任务")
    @GetMapping("/hot")
    public AjaxResult getHotTasks(
            @ApiParam("经度") @RequestParam(required = false) String longitude,
            @ApiParam("纬度") @RequestParam(required = false) String latitude,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<AppTask> tasks = appTaskService.selectHotTaskList(longitude, latitude, limit);
        return success(tasks);
    }
}
