-- ----------------------------
-- 支付管理菜单权限配置
-- ----------------------------

-- 支付管理主目录
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4000, '支付管理', 0, 8, 'payment', NULL, '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', sysdate(), '', NULL, '支付管理目录');

-- ----------------------------
-- 任务支付管理
-- ----------------------------

-- 任务支付管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4010, '任务支付管理', 4000, 1, 'taskPayment', 'fuguang/taskPayment/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:taskPayment:list', 'shopping', 'admin', sysdate(), '', NULL, '任务支付管理菜单');

-- 任务支付管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4011, '任务支付查询', 4010, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskPayment:query', '#', 'admin', sysdate(), '', NULL, ''),
(4012, '任务支付新增', 4010, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskPayment:add', '#', 'admin', sysdate(), '', NULL, ''),
(4013, '任务支付修改', 4010, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskPayment:edit', '#', 'admin', sysdate(), '', NULL, ''),
(4014, '任务支付删除', 4010, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskPayment:remove', '#', 'admin', sysdate(), '', NULL, ''),
(4015, '任务支付导出', 4010, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskPayment:export', '#', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 商城支付管理
-- ----------------------------

-- 商城支付管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4020, '商城支付管理', 4000, 2, 'mallPayment', 'mall/payment/index', '', '', 1, 0, 'C', '0', '0', 'mall:payment:list', 'goods', 'admin', sysdate(), '', NULL, '商城支付管理菜单');

-- 商城支付管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4021, '商城支付查询', 4020, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:payment:query', '#', 'admin', sysdate(), '', NULL, ''),
(4022, '商城支付新增', 4020, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:payment:add', '#', 'admin', sysdate(), '', NULL, ''),
(4023, '商城支付修改', 4020, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:payment:edit', '#', 'admin', sysdate(), '', NULL, ''),
(4024, '商城支付删除', 4020, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:payment:remove', '#', 'admin', sysdate(), '', NULL, ''),
(4025, '商城支付导出', 4020, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'mall:payment:export', '#', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 线下支付管理（更新现有菜单到支付管理目录下）
-- ----------------------------

-- 更新线下支付管理主菜单到支付管理目录下
UPDATE `sys_menu` SET `parent_id` = 4000, `order_num` = 3, `menu_name` = '线下支付管理' WHERE `menu_id` = 3055;

-- ----------------------------
-- 支付配置管理
-- ----------------------------

-- 支付配置管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4030, '支付配置管理', 4000, 4, 'paymentConfig', 'payment/config/index', '', '', 1, 0, 'C', '0', '0', 'payment:config:list', 'tool', 'admin', sysdate(), '', NULL, '支付配置管理菜单');

-- 支付配置管理按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4031, '支付配置查询', 4030, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:config:query', '#', 'admin', sysdate(), '', NULL, ''),
(4032, '支付配置修改', 4030, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:config:edit', '#', 'admin', sysdate(), '', NULL, ''),
(4033, '支付配置测试', 4030, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:config:test', '#', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 支付统计分析
-- ----------------------------

-- 支付统计分析主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4040, '支付统计分析', 4000, 5, 'paymentStatistics', 'payment/statistics/index', '', '', 1, 0, 'C', '0', '0', 'payment:statistics:list', 'chart', 'admin', sysdate(), '', NULL, '支付统计分析菜单');

-- 支付统计分析按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4041, '支付统计查询', 4040, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:statistics:query', '#', 'admin', sysdate(), '', NULL, ''),
(4042, '支付趋势分析', 4040, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:statistics:trend', '#', 'admin', sysdate(), '', NULL, ''),
(4043, '支付渠道分析', 4040, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:statistics:channel', '#', 'admin', sysdate(), '', NULL, ''),
(4044, '支付异常分析', 4040, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:statistics:exception', '#', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 支付回调日志
-- ----------------------------

-- 支付回调日志主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4050, '支付回调日志', 4000, 6, 'paymentCallback', 'payment/callback/index', '', '', 1, 0, 'C', '0', '0', 'payment:callback:list', 'log', 'admin', sysdate(), '', NULL, '支付回调日志菜单');

-- 支付回调日志按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(4051, '回调日志查询', 4050, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:callback:query', '#', 'admin', sysdate(), '', NULL, ''),
(4052, '回调日志导出', 4050, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:callback:export', '#', 'admin', sysdate(), '', NULL, ''),
(4053, '回调重试', 4050, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'payment:callback:retry', '#', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 添加字典数据
-- ----------------------------

-- 支付类型字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(100, '支付类型', 'pay_type', '0', 'admin', sysdate(), '支付方式类型');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(100, 1, '支付宝', '1', 'pay_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '支付宝支付'),
(101, 2, '微信支付', '2', 'pay_type', '', 'success', 'N', '0', 'admin', sysdate(), '微信支付'),
(102, 3, '银联支付', '3', 'pay_type', '', 'info', 'N', '0', 'admin', sysdate(), '银联支付');

-- 支付状态字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(101, '支付状态', 'pay_status', '0', 'admin', sysdate(), '支付状态');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(103, 1, '待支付', '0', 'pay_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '待支付状态'),
(104, 2, '支付成功', '1', 'pay_status', '', 'success', 'N', '0', 'admin', sysdate(), '支付成功状态'),
(105, 3, '支付失败', '2', 'pay_status', '', 'danger', 'N', '0', 'admin', sysdate(), '支付失败状态'),
(106, 4, '已退款', '3', 'pay_status', '', 'info', 'N', '0', 'admin', sysdate(), '已退款状态');

-- 转账状态字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(102, '转账状态', 'transfer_status', '0', 'admin', sysdate(), '转账状态');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(107, 1, '未转账', '0', 'transfer_status', '', 'info', 'Y', '0', 'admin', sysdate(), '未转账状态'),
(108, 2, '转账中', '1', 'transfer_status', '', 'warning', 'N', '0', 'admin', sysdate(), '转账中状态'),
(109, 3, '转账成功', '2', 'transfer_status', '', 'success', 'N', '0', 'admin', sysdate(), '转账成功状态'),
(110, 4, '转账失败', '3', 'transfer_status', '', 'danger', 'N', '0', 'admin', sysdate(), '转账失败状态');
