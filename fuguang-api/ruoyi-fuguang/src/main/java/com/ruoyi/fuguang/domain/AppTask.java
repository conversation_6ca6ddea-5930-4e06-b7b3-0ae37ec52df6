package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP任务对象 app_task
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 任务标题 */
    @Excel(name = "任务标题")
    private String taskTitle;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String taskDesc;

    /** 任务金额 */
    @Excel(name = "任务金额")
    private BigDecimal taskAmount;

    /** 平台维护费比例 */
    @Excel(name = "平台维护费比例")
    private BigDecimal maintenanceFeeProportion;

    /** 保障金比例 */
    @Excel(name = "保障金比例")
    private BigDecimal guaranteeProportion;

    /** 任务类型（0普通任务 1紧急任务） */
    @Excel(name = "任务类型", readConverterExp = "0=普通任务,1=紧急任务")
    private String taskType;

    /** 一级任务类型ID */
    @Excel(name = "一级任务类型ID")
    private Long firstTypeId;

    /** 二级任务类型ID */
    @Excel(name = "二级任务类型ID")
    private Long secondTypeId;

    /** 紧急程度（0普通 1紧急 2非常紧急） */
    @Excel(name = "紧急程度", readConverterExp = "0=普通,1=紧急,2=非常紧急")
    private String urgentLevel;

    /** 任务状态（0待接取 1进行中 2已完成 3已取消） */
    @Excel(name = "任务状态", readConverterExp = "0=待接取,1=进行中,2=已完成,3=已取消")
    private String taskStatus;

    /** 发布者ID */
    @Excel(name = "发布者ID")
    private Long publisherId;

    /** 发布者昵称 */
    @Excel(name = "发布者昵称")
    private String publisherName;

    /** 发布者头像 */
    private String publisherAvatar;

    /** 接收者ID */
    @Excel(name = "接收者ID")
    private Long receiverId;

    /** 接收者昵称 */
    @Excel(name = "接收者昵称")
    private String receiverName;

    /** 任务地址 */
    @Excel(name = "任务地址")
    private String taskAddress;

    /** 经度 */
    private String longitude;

    /** 纬度 */
    private String latitude;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 热度分数 */
    @Excel(name = "热度分数")
    private Integer hotScore;

    /** 任务图片列表（用于接收前端数据，不存储到数据库） */
    private List<Object> images;

    /** 任务图片列表（用于返回给前端，不存储到数据库） */
    private List<AppTaskImage> taskImages;

    /** 发布者个人简介（用于返回给前端，不存储到数据库） */
    private AppUserProfile publisherUserProfile;

    /** 接受者个人简介（用于返回给前端，不存储到数据库） */
    private AppUserProfile receiverUserProfile;

    /** 一级任务类型名称（用于返回给前端，不存储到数据库） */
    private String firstTypeName;

    /** 二级任务类型名称（用于返回给前端，不存储到数据库） */
    private String secondTypeName;


}
