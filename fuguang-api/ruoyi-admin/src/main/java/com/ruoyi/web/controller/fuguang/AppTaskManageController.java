package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP任务管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController("appTaskManageController")
@RequestMapping("/fuguang/task")
public class AppTaskManageController extends BaseController
{
    @Autowired
    private IAppTaskService appTaskService;

    /**
     * 查询APP任务列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppTask appTask)
    {
        startPage();
        List<AppTask> list = appTaskService.selectAppTaskList(appTask);
        return getDataTable(list);
    }

    /**
     * 导出APP任务列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:export')")
    @Log(title = "APP任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppTask appTask)
    {
        List<AppTask> list = appTaskService.selectAppTaskList(appTask);
        ExcelUtil<AppTask> util = new ExcelUtil<AppTask>(AppTask.class);
        util.exportExcel(response, list, "APP任务数据");
    }

    /**
     * 获取APP任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId)
    {
        return success(appTaskService.selectAppTaskByTaskId(taskId));
    }

    /**
     * 新增APP任务
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:add')")
    @Log(title = "APP任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppTask appTask)
    {
        appTask.setCreateBy(getUsername());
        return success(appTaskService.insertAppTask(appTask));
    }

    /**
     * 修改APP任务
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:edit')")
    @Log(title = "APP任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppTask appTask)
    {
        appTask.setUpdateBy(getUsername());
        return toAjax(appTaskService.updateAppTask(appTask));
    }

    /**
     * 删除APP任务
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:remove')")
    @Log(title = "APP任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds)
    {
        return toAjax(appTaskService.deleteAppTaskByTaskIds(taskIds));
    }

    /**
     * 任务审核
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:audit')")
    @Log(title = "APP任务审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult auditTask(@RequestBody AppTask appTask)
    {
        appTask.setUpdateBy(getUsername());
        return toAjax(appTaskService.updateAppTask(appTask));
    }

    /**
     * 完成任务
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:complete')")
    @Log(title = "完成任务", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{taskId}")
    public AjaxResult forceCompleteTask(@PathVariable Long taskId)
    {
        return appTaskService.completeTask(taskId);
    }

    /**
     * 取消任务
     */
    @PreAuthorize("@ss.hasPermi('fuguang:task:cancel')")
    @Log(title = "取消任务", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{taskId}")
    public AjaxResult forceCancelTask(@PathVariable Long taskId)
    {
        return toAjax(appTaskService.cancelTask(taskId));
    }
}
