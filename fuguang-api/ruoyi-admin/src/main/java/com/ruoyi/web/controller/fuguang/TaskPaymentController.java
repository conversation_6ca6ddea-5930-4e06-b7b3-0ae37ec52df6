package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.TaskPayment;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 任务支付记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/fuguang/taskPayment")
public class TaskPaymentController extends BaseController
{
    @Autowired
    private ITaskPaymentService taskPaymentService;

    /**
     * 查询任务支付记录列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskPayment taskPayment)
    {
        startPage();
        List<TaskPayment> list = taskPaymentService.selectTaskPaymentList(taskPayment);
        return getDataTable(list);
    }

    /**
     * 导出任务支付记录列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:export')")
    @Log(title = "任务支付记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskPayment taskPayment)
    {
        List<TaskPayment> list = taskPaymentService.selectTaskPaymentList(taskPayment);
        ExcelUtil<TaskPayment> util = new ExcelUtil<TaskPayment>(TaskPayment.class);
        util.exportExcel(response, list, "任务支付记录数据");
    }

    /**
     * 获取任务支付记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:query')")
    @GetMapping(value = "/{paymentId}")
    public AjaxResult getInfo(@PathVariable("paymentId") Long paymentId)
    {
        return success(taskPaymentService.selectTaskPaymentByPaymentId(paymentId));
    }

    /**
     * 新增任务支付记录
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:add')")
    @Log(title = "任务支付记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskPayment taskPayment)
    {
        return toAjax(taskPaymentService.insertTaskPayment(taskPayment));
    }

    /**
     * 修改任务支付记录
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:edit')")
    @Log(title = "任务支付记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskPayment taskPayment)
    {
        return toAjax(taskPaymentService.updateTaskPayment(taskPayment));
    }

    /**
     * 删除任务支付记录
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:remove')")
    @Log(title = "任务支付记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paymentIds}")
    public AjaxResult remove(@PathVariable Long[] paymentIds)
    {
        return toAjax(taskPaymentService.deleteTaskPaymentByPaymentIds(paymentIds));
    }

    /**
     * 根据订单号查询支付记录
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:query')")
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable String orderNo)
    {
        return success(taskPaymentService.selectTaskPaymentByOrderNo(orderNo));
    }

    /**
     * 查询支付状态
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:query')")
    @GetMapping("/status/{orderNo}")
    public AjaxResult getPaymentStatus(@PathVariable String orderNo)
    {
        String status = taskPaymentService.getPaymentStatus(orderNo);
        return success(status);
    }

    /**
     * 获取支付统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskPayment:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 查询各种状态的订单数量和金额
        TaskPayment query = new TaskPayment();
        
        // 待支付订单
        query.setPayStatus("0");
        List<TaskPayment> pendingList = taskPaymentService.selectTaskPaymentList(query);
        int pendingCount = pendingList.size();
        BigDecimal pendingAmount = pendingList.stream()
            .map(TaskPayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 支付成功订单
        query.setPayStatus("1");
        List<TaskPayment> successList = taskPaymentService.selectTaskPaymentList(query);
        int successCount = successList.size();
        BigDecimal successAmount = successList.stream()
            .map(TaskPayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 支付失败订单
        query.setPayStatus("2");
        List<TaskPayment> failedList = taskPaymentService.selectTaskPaymentList(query);
        int failedCount = failedList.size();
        BigDecimal failedAmount = failedList.stream()
            .map(TaskPayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 构建统计结果
        final BigDecimal finalPendingAmount = pendingAmount;
        final BigDecimal finalSuccessAmount = successAmount;
        final BigDecimal finalFailedAmount = failedAmount;

        // 构建统计结果
        return success(new Object() {
            public final int pendingPayments = pendingCount;
            public final BigDecimal pendingAmount = finalPendingAmount;
            public final int successPayments = successCount;
            public final BigDecimal successAmount = finalSuccessAmount;
            public final int failedPayments = failedCount;
            public final BigDecimal failedAmount = finalFailedAmount;
            public final int totalPayments = pendingCount + successCount + failedCount;
            public final BigDecimal totalAmount = pendingAmount.add(successAmount).add(failedAmount);
        });
    }
}
